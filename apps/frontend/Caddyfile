# Global options optimized for Railway
{
    admin off # No need for admin API in Railway's environment
    persist_config off # Storage isn't persistent anyway
    auto_https off # Railway handles HTTPS for us
    # Runtime logs
    log {
        format json # Set runtime log format to JSON mode
    }
    # Server options
    servers {
        trusted_proxies static private_ranges 100.0.0.0/8 # Trust Railway's proxy
    }
}

# Site block, listens on the $PORT environment variable, automatically assigned by Railway
:{$PORT:3000} {
    # Access logs
    log {
        format json # Set access log format to JSON mode
    }

    # Health check for Railway
    rewrite /health /*

    # Proxy API requests to backend using Railway's internal networking
    handle_path /api/* {
        reverse_proxy https://backend-production-9857.up.railway.app {
            header_up Host {http.reverse_proxy.upstream.hostport}
            header_up X-Real-IP {http.request.remote.host}
            header_up X-Forwarded-For {http.request.remote.host}
            header_up X-Forwarded-Proto {http.request.scheme}
        }
    }

    # Serve static files from the build output
    handle {
        root * /assets
        
        # Enable gzip compression
        encode gzip
        
        # Serve files from build output
        file_server
        
        # SPA fallback - if path doesn't exist, redirect to index.html for client-side routing
        try_files {path} /index.html
    }
}
