/**
 * Funções utilitárias para chamar a API FastAPI do DataHero.
 * O endereço base é definido por `VITE_API_BASE_URL` no ambiente de build.
 */

export interface AskResponse {
  query_id: string;
  question: string;
  sql_query?: string;
  results?: Record<string, unknown>;
  direct_answer?: string;
  analysis_level?: string;
  visualization_data?: Record<string, unknown>;
  error?: string;
}

export interface FeedbackPayload {
  query_id: string;
  feedback_text: string;
  original_question: string;
  corrected_sql?: string;
  user_id?: string;
}

export interface SimpleFeedbackPayload {
  query_id: string;
  feedback_type: 'positive' | 'negative';
  original_question: string;
  category?: string;
  comment?: string;
  user_id?: string;
}

export interface FeedbackReprocessPayload {
  feedback: SimpleFeedbackPayload;
  reprocess: boolean;
}

export interface KpiAlert {
  type: 'above' | 'below';
  threshold: number;
  message?: string;
}

export interface AvailableKpi {
  id: string;
  name: string;
  description: string;
  category: string;
  unit: string;
  icon: string;
}

export interface KpiData {
  id: string;
  title: string;
  description: string;
  currentValue: number;
  format: 'currency' | 'percentage' | 'number';
  changePercent?: number;
  trend: 'up' | 'down' | 'stable';
  chartType: 'line' | 'area' | 'bar';
  chartData: Array<{ name: string; value: number }>;
  alert?: KpiAlert;
  isPriority: boolean;
  order: number;
  category: string;
  unit?: string;
  frequency?: string;
}

export interface DashboardKpisResponse {
  kpis: KpiData[];
  total_count: number;
  sector: string;
  client_id: string;
  timeframe: string;
  generated_at: string;
}

export interface SingleKpiResponse {
  kpi: KpiData;
  calculated_at: string;
}

export interface DashboardSummaryResponse {
  kpis: KpiData[];
  summary: Record<string, any>;
  metadata: Record<string, any>;
}

export interface SnapshotKpiData {
  value: number;
  formatted: string;
  title: string;
  description: string;
  icon: string;
  format: 'currency' | 'percentage' | 'number';
  unit: string;
  category: string;
  status?: 'success' | 'error';
  error?: string;
}

export interface DashboardSnapshotResponse {
  success: boolean;
  data: Record<string, SnapshotKpiData>;
  metadata: {
    generated_at: string;
    client_id: string;
    kpi_count: number;
    cache_ttl: number;
    next_update: string;
  };
  summary: {
    total_calculated: number;
    total_expected: number;
    total_failed?: number;
    success_rate: number;
  };
}

// Railway deployment: frontend e backend são serviços separados
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://backend-production-9857.up.railway.app';

// Debug logging
console.log('🔍 [API DEBUG] API_BASE_URL:', API_BASE_URL);
console.log('🔍 [API DEBUG] VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);
console.log('🔍 [API DEBUG] All env vars:', import.meta.env);
console.log('🚀 [DEPLOY] Force rebuild timestamp:', new Date().toISOString());

/**
 * Envia uma pergunta para o endpoint /ask.
 * @param question Pergunta do usuário.
 * @returns Resposta estruturada do backend.
 */
export const askQuestion = async (question: string): Promise<AskResponse> => {
  const url = `${API_BASE_URL}/ask`;
  const payload = {
    question,
    sector: 'cambio'   // Default sector for frontend
  };
  
  console.log('🚀 [API] Making request to:', url);
  console.log('🚀 [API] Payload:', payload);
  
  const resp = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  console.log('📡 [API] Response status:', resp.status);
  console.log('📡 [API] Response headers:', [...resp.headers.entries()]);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('❌ [API] Error response:', errText);
    throw new Error(`Erro API /ask: ${resp.status} – ${errText}`);
  }

  const data = await resp.json() as AskResponse;
  console.log('✅ [API] Success response:', data);
  return data;
};

/**
 * Envia feedback (positivo/negativo) para o endpoint /feedback.
 * @param payload Dados de feedback.
 */
export const sendFeedback = async (payload: FeedbackPayload): Promise<void> => {
  const resp = await fetch(`${API_BASE_URL}/feedback`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!resp.ok) {
    const errText = await resp.text();
    throw new Error(`Erro API /feedback: ${resp.status} – ${errText}`);
  }
};

/**
 * Envia feedback simples (positivo/negativo) para o endpoint /feedback/simple.
 * @param payload Dados de feedback simples.
 */
export const sendSimpleFeedback = async (payload: SimpleFeedbackPayload): Promise<void> => {
  console.log('🔥 [API] sendSimpleFeedback called with:', payload);

  const resp = await fetch(`${API_BASE_URL}/feedback/simple`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  console.log('🔥 [API] Simple feedback response status:', resp.status);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('🔥 [API] Simple feedback error:', errText);
    throw new Error(`Erro API /feedback/simple: ${resp.status} – ${errText}`);
  }

  console.log('🔥 [API] Simple feedback sent successfully');
};

/**
 * Envia feedback e imediatamente reprocessa a pergunta com correções inteligentes.
 * @param payload Dados de feedback e configuração de reprocessamento.
 * @returns Nova resposta reprocessada ou void se apenas feedback.
 */
export const sendFeedbackAndReprocess = async (payload: FeedbackReprocessPayload): Promise<AskResponse | void> => {
  console.log('🔥 [API] sendFeedbackAndReprocess called with:', payload);

  const resp = await fetch(`${API_BASE_URL}/feedback/reprocess`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  console.log('🔥 [API] Response status:', resp.status);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('🔥 [API] Error response:', errText);
    throw new Error(`Erro API /feedback/reprocess: ${resp.status} – ${errText}`);
  }

  // Se reprocessamento foi solicitado, retorna a nova resposta
  if (payload.reprocess) {
    const result = await resp.json() as AskResponse;
    console.log('🔥 [API] Reprocessed response:', result);
    return result;
  }
};

/**
 * Get dashboard KPIs with calculated values.
 * @param sector Business sector (default: 'cambio')
 * @param client_id Client identifier (default: 'L2M')
 * @param timeframe Time frame for calculations (default: '1d')
 * @param category Optional category filter
 * @returns Dashboard KPIs response
 */
export const getDashboardKpis = async (
  sector: string = 'cambio',
  client_id: string = 'L2M',
  timeframe: string = '1d',
  category?: string,
  priority_only: boolean = true,
  currency: string = 'all'
): Promise<DashboardKpisResponse> => {
  const params = new URLSearchParams({
    sector,
    client_id,
    timeframe,
    priority_only: priority_only.toString(),
    currency
  });

  if (category) {
    params.append('category', category);
  }

  const url = `${API_BASE_URL}/api/dashboard/kpis?${params.toString()}`;

  console.log('🚀 [API] Getting dashboard KPIs from:', url);

  const resp = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  console.log('📡 [API] Dashboard KPIs response status:', resp.status);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('❌ [API] Error response:', errText);
    throw new Error(`Erro API /api/dashboard/kpis: ${resp.status} – ${errText}`);
  }

  const data = await resp.json() as DashboardKpisResponse;

  console.log('✅ [API] Dashboard KPIs loaded successfully:', data);
  return data;
};

/**
 * Get dashboard snapshot with pre-calculated critical KPIs for L2M business
 * This is much faster than getDashboardKpis as it uses pre-calculated values
 *
 * @param regenerate Force regeneration of snapshot (dev mode only)
 * @returns Dashboard snapshot response
 */
export const getDashboardSnapshot = async (
  regenerate: boolean = false
): Promise<DashboardSnapshotResponse> => {
  const params = new URLSearchParams();

  if (regenerate) {
    params.append('regenerate', 'true');
  }

  const url = `${API_BASE_URL}/api/dashboard/snapshot?${params.toString()}`;

  console.log('🚀 [API] Getting dashboard snapshot from:', url);

  const resp = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  console.log('📡 [API] Dashboard snapshot response status:', resp.status);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('❌ [API] Error response:', errText);
    throw new Error(`Erro API /api/dashboard/snapshot: ${resp.status} – ${errText}`);
  }

  const data = await resp.json() as DashboardSnapshotResponse;

  console.log('✅ [API] Dashboard snapshot loaded successfully:', data);
  return data;
};

/**
 * Convert snapshot data to KpiData format for compatibility with existing components
 */
export const convertSnapshotToKpiData = (snapshot: DashboardSnapshotResponse): KpiData[] => {
  const kpis: KpiData[] = [];

  Object.entries(snapshot.data).forEach(([kpiId, kpiData], index) => {
    // Chart data should come from backend, use empty array if not available
    const chartData: Array<{ name: string; value: number }> = [];

    kpis.push({
      id: kpiId,
      title: kpiData.title,
      description: kpiData.description,
      currentValue: kpiData.value || 0,
      format: kpiData.format,
      changePercent: 0, // Should come from backend
      trend: 'stable', // Should come from backend
      chartType: kpiId.includes('volume') ? 'area' : 'line',
      chartData,
      isPriority: index < 3, // First 3 are priority
      order: index,
      category: kpiData.category,
      unit: kpiData.unit,
      frequency: 'daily'
    });
  });

  return kpis;
};

/**
 * Calculate a specific KPI on demand.
 * @param kpi_id KPI identifier
 * @param sector Business sector (default: 'cambio')
 * @param client_id Client identifier (default: 'L2M')
 * @returns Single KPI response
 */
export const calculateKpi = async (
  kpi_id: string,
  sector: string = 'cambio',
  client_id: string = 'L2M'
): Promise<SingleKpiResponse> => {
  const params = new URLSearchParams({
    sector,
    client_id
  });

  const url = `${API_BASE_URL}/api/kpis/${kpi_id}/calculate?${params.toString()}`;

  console.log('🚀 [API] Calculating KPI from:', url);

  const resp = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  console.log('📡 [API] Calculate KPI response status:', resp.status);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('❌ [API] Error response:', errText);
    throw new Error(`Erro API /api/kpis/${kpi_id}/calculate: ${resp.status} – ${errText}`);
  }

  const data = await resp.json() as SingleKpiResponse;
  console.log('✅ [API] Calculate KPI success:', data);
  return data;
};

/**
 * Get complete dashboard summary with all KPIs and metadata.
 * @param sector Business sector (default: 'cambio')
 * @param client_id Client identifier (default: 'L2M')
 * @param timeframe Time frame for calculations (default: '1d')
 * @returns Dashboard summary response
 */
export const getDashboardSummary = async (
  sector: string = 'cambio',
  client_id: string = 'L2M',
  timeframe: string = '1d'
): Promise<DashboardSummaryResponse> => {
  const params = new URLSearchParams({
    sector,
    client_id,
    timeframe
  });

  const url = `${API_BASE_URL}/api/dashboard?${params.toString()}`;

  console.log('🚀 [API] Getting dashboard summary from:', url);

  const resp = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  console.log('📡 [API] Dashboard summary response status:', resp.status);

  if (!resp.ok) {
    const errText = await resp.text();
    console.error('❌ [API] Error response:', errText);
    throw new Error(`Erro API /api/dashboard: ${resp.status} – ${errText}`);
  }

  const data = await resp.json() as DashboardSummaryResponse;
  console.log('✅ [API] Dashboard summary success:', data);
  return data;
};

/**
 * Get available KPIs for selection - uses real backend data
 * @param client_id Client identifier (default: 'L2M')
 * @param sector Business sector (default: 'cambio')
 * @returns List of available KPIs from backend
 */
export const getAvailableKpis = async (
  sector: string = 'cambio'
): Promise<AvailableKpi[]> => {
  console.log('📡 [API] Getting available KPIs from backend...');

  try {
    // First try to get KPIs from the dashboard snapshot which contains real data
    const snapshot = await getDashboardSnapshot();

    // Convert snapshot data to AvailableKpi format
    const availableKpis: AvailableKpi[] = Object.entries(snapshot.data).map(([kpiId, kpiData]) => ({
      id: kpiId,
      name: kpiData.title || kpiId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: kpiData.description || `KPI for ${kpiId}`,
      category: kpiData.category || 'General',
      unit: kpiData.unit || 'Number',
      icon: getKpiIcon(kpiId)
    }));

    console.log('✅ [API] Available KPIs loaded from real data:', availableKpis.length);
    return availableKpis;

  } catch (error) {
    console.error('❌ [API] Error loading available KPIs from backend:', error);
    // Only fallback to basic structure if backend is completely unavailable
    throw error;
  }
};

/**
 * Get appropriate icon for KPI based on its ID
 */
const getKpiIcon = (kpiId: string): string => {
  const iconMap: Record<string, string> = {
    'total_volume': '📊',
    'volume_by_currency': '💱',
    'average_ticket': '🎫',
    'growth_percentage': '📈',
    'average_spread': '💰',
    'gross_margin': '💵',
    'net_margin': '🏦',
    'operations_roi': '📊',
    'cost_per_operation': '💳',
    'cost_to_income_ratio': '⚖️',
    'transaction_count': '🔢',
    'processing_time': '⏱️',
    'throughput': '⚡',
    'approval_rate': '✅',
    'rejection_rate': '❌',
    'operational_efficiency': '⚙️',
    'capacity_utilization': '📊',
    'var': '🛡️',
    'exposure_concentration': '🎯',
    'currency_risk': '💱',
    'counterparty_risk': '🤝',
    'compliance_score': '📋'
  };

  return iconMap[kpiId] || '📊';
};


