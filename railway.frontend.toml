[build]
builder = "dockerfile"
dockerfilePath = "apps/frontend/Dockerfile"

[deploy]
startCommand = "nginx -g 'daemon off;'"
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[environments.production.variables]
NODE_ENV = "production"
VITE_API_URL = "https://backend-production-9857.up.railway.app"
VITE_API_BASE_URL = "https://backend-production-9857.up.railway.app"